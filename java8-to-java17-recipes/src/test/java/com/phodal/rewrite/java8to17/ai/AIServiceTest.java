package com.phodal.rewrite.java8to17.ai;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.*;

/**
 * Test class for AI Service functionality
 * Note: These tests require actual API keys and are disabled by default
 */
class AIServiceTest {
    
    private AIService aiService;
    
    @BeforeEach
    void setUp() {
        // Load environment for testing
        EnvironmentLoader.loadEnvironment();
    }
    
    @Test
    void testChatMessageCreation() {
        ChatMessage systemMsg = ChatMessage.system("You are a helpful assistant");
        ChatMessage userMsg = ChatMessage.user("Hello");
        ChatMessage assistantMsg = ChatMessage.assistant("Hi there!");
        
        assertThat(systemMsg.getRole()).isEqualTo("system");
        assertThat(systemMsg.getContent()).isEqualTo("You are a helpful assistant");
        
        assertThat(userMsg.getRole()).isEqualTo("user");
        assertThat(userMsg.getContent()).isEqualTo("Hello");
        
        assertThat(assistantMsg.getRole()).isEqualTo("assistant");
        assertThat(assistantMsg.getContent()).isEqualTo("Hi there!");
    }
    
    @Test
    void testAIServiceConfigCreation() {
        AIServiceConfig chatGLMConfig = AIServiceConfig.forChatGLM("test-key");
        assertThat(chatGLMConfig.getApiKey()).isEqualTo("test-key");
        assertThat(chatGLMConfig.getBaseUrl()).isEqualTo("https://open.bigmodel.cn/api/paas/v4");
        assertThat(chatGLMConfig.getModel()).isEqualTo("glm-4");
        
        AIServiceConfig deepSeekConfig = AIServiceConfig.forDeepSeek("test-key");
        assertThat(deepSeekConfig.getApiKey()).isEqualTo("test-key");
        assertThat(deepSeekConfig.getBaseUrl()).isEqualTo("https://api.deepseek.com");
        assertThat(deepSeekConfig.getModel()).isEqualTo("deepseek-chat");
        
        AIServiceConfig openAIConfig = AIServiceConfig.forOpenAI("test-key");
        assertThat(openAIConfig.getApiKey()).isEqualTo("test-key");
        assertThat(openAIConfig.getBaseUrl()).isEqualTo("https://api.openai.com/v1");
        assertThat(openAIConfig.getModel()).isEqualTo("gpt-3.5-turbo");
    }
    
    @Test
    void testFactoryMethodsWithInvalidKeys() {
        assertThatThrownBy(() -> AIServiceFactory.createChatGLMService(null))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("ChatGLM API key is required");
        
        assertThatThrownBy(() -> AIServiceFactory.createChatGLMService(""))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("ChatGLM API key is required");
        
        assertThatThrownBy(() -> AIServiceFactory.createDeepSeekService(null))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("DeepSeek API key is required");
        
        assertThatThrownBy(() -> AIServiceFactory.createOpenAIService(null))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("OpenAI API key is required");
    }
    
    @Test
    void testEnvironmentLoader() {
        // Test that environment loader doesn't throw exceptions
        assertThatCode(() -> EnvironmentLoader.loadEnvironment()).doesNotThrowAnyException();
        
        // Test getEnv with default value
        String value = EnvironmentLoader.getEnv("NON_EXISTENT_KEY", "default");
        assertThat(value).isEqualTo("default");
    }
    
    @Test
    @Disabled("Requires actual API key - enable for integration testing")
    void testChatGLMServiceIntegration() {
        String apiKey = EnvironmentLoader.getEnv("GLM_API_KEY");
        if (apiKey == null) {
            return; // Skip test if no API key
        }
        
        aiService = AIServiceFactory.createChatGLMService(apiKey);
        
        assertThat(aiService.getProviderName()).isEqualTo("ChatGLM");
        
        String response = aiService.sendMessage("Hello, this is a test message.");
        assertThat(response).isNotNull();
        assertThat(response).isNotEmpty();
    }
    
    @Test
    @Disabled("Requires actual API key - enable for integration testing")
    void testDeepSeekServiceIntegration() {
        String apiKey = EnvironmentLoader.getEnv("DEEPSEEK_TOKEN");
        if (apiKey == null) {
            return; // Skip test if no API key
        }
        
        aiService = AIServiceFactory.createDeepSeekService(apiKey);
        
        assertThat(aiService.getProviderName()).isEqualTo("DeepSeek");
        
        List<ChatMessage> conversation = Arrays.asList(
                ChatMessage.system("You are a helpful assistant."),
                ChatMessage.user("What is Java?")
        );
        
        String response = aiService.sendConversation(conversation);
        assertThat(response).isNotNull();
        assertThat(response).isNotEmpty();
    }
    
    @Test
    @Disabled("Requires actual API key - enable for integration testing")
    void testServiceFromEnvironment() {
        try {
            aiService = AIServiceFactory.createFromEnvironment();
            assertThat(aiService).isNotNull();
            assertThat(aiService.getProviderName()).isNotNull();
        } catch (IllegalStateException e) {
            // Expected if no API keys are configured
            assertThat(e.getMessage()).contains("No AI service API key found");
        }
    }
}
