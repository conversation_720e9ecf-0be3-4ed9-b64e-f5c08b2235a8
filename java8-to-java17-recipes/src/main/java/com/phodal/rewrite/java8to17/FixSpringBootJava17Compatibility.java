/*
 * Copyright 2024 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.phodal.rewrite.java8to17;

import lombok.EqualsAndHashCode;
import lombok.Value;
import org.openrewrite.*;
import org.openrewrite.java.JavaIsoVisitor;
import org.openrewrite.java.tree.*;
import org.openrewrite.marker.Markers;

import java.util.Collections;

@EqualsAndHashCode(callSuper = false)
public class FixSpringBootJava17Compatibility extends Recipe {

    @Override
    public String getDisplayName() {
        return "Fix Spring Boot Java 17 compatibility issues";
    }

    @Override
    public String getDescription() {
        return "Fix Spring Boot specific compatibility issues when upgrading to Java 17, " +
               "including deprecated annotations and configuration changes.";
    }

    @Override
    public TreeVisitor<?, ExecutionContext> getVisitor() {
        return new JavaIsoVisitor<ExecutionContext>() {
            
            @Override
            public J.Annotation visitAnnotation(J.Annotation annotation, ExecutionContext ctx) {
                J.Annotation a = super.visitAnnotation(annotation, ctx);
                
                // Handle deprecated Spring Security annotations
                if (a.getAnnotationType() instanceof J.Identifier) {
                    J.Identifier annotationType = (J.Identifier) a.getAnnotationType();
                    String annotationName = annotationType.getSimpleName();
                    
                    // Replace @EnableWebMvcSecurity with @EnableWebSecurity
                    if ("EnableWebMvcSecurity".equals(annotationName)) {
                        return a.withAnnotationType(annotationType.withSimpleName("EnableWebSecurity"));
                    }
                }
                
                return a;
            }
            
            @Override
            public J.ClassDeclaration visitClassDeclaration(J.ClassDeclaration classDecl, ExecutionContext ctx) {
                J.ClassDeclaration c = super.visitClassDeclaration(classDecl, ctx);
                
                // Handle WebMvcConfigurerAdapter deprecation
                if (c.getExtends() != null && c.getExtends().getType() != null) {
                    String extendsType = c.getExtends().getType().toString();
                    if (extendsType.contains("WebMvcConfigurerAdapter")) {
                        // Add comment about WebMvcConfigurerAdapter deprecation
                        String comment = "WebMvcConfigurerAdapter is deprecated. " +
                                       "Implement WebMvcConfigurer interface directly instead.";
                        return c.withComments(Collections.singletonList(
                            new TextComment(true, comment, "\n", Markers.EMPTY)));
                    }
                }
                
                return c;
            }
            
            @Override
            public J.MethodDeclaration visitMethodDeclaration(J.MethodDeclaration method, ExecutionContext ctx) {
                J.MethodDeclaration m = super.visitMethodDeclaration(method, ctx);
                
                // Handle deprecated Spring Boot configuration methods
                String methodName = m.getSimpleName();
                
                // Handle deprecated EmbeddedServletContainerCustomizer
                if ("customize".equals(methodName) && 
                    hasParameterType(m, "EmbeddedServletContainerFactory")) {
                    String comment = "EmbeddedServletContainerCustomizer is deprecated. " +
                                   "Use WebServerFactoryCustomizer<ConfigurableWebServerFactory> instead.";
                    return m.withComments(Collections.singletonList(
                        new TextComment(true, comment, "\n", Markers.EMPTY)));
                }
                
                return m;
            }
            
            @Override
            public J.Import visitImport(J.Import anImport, ExecutionContext ctx) {
                J.Import i = super.visitImport(anImport, ctx);
                
                String importName = i.getQualid().printTrimmed();
                
                // Handle deprecated Spring Boot imports
                if (importName.contains("EmbeddedServletContainerCustomizer") ||
                    importName.contains("EmbeddedServletContainerFactory") ||
                    importName.contains("TomcatEmbeddedServletContainerFactory")) {
                    String comment = "This class is deprecated in newer Spring Boot versions. " +
                                   "Use WebServerFactoryCustomizer and related classes instead.";
                    return i.withComments(Collections.singletonList(
                        new TextComment(true, comment, "\n", Markers.EMPTY)));
                }
                
                // Handle deprecated Spring Security imports
                if (importName.contains("EnableWebMvcSecurity")) {
                    String comment = "@EnableWebMvcSecurity is deprecated. Use @EnableWebSecurity instead.";
                    return i.withComments(Collections.singletonList(
                        new TextComment(true, comment, "\n", Markers.EMPTY)));
                }
                
                return i;
            }
            
            private boolean hasParameterType(J.MethodDeclaration method, String typeName) {
                return method.getParameters().stream()
                    .anyMatch(param -> {
                        if (param instanceof J.VariableDeclarations) {
                            J.VariableDeclarations varDecl = (J.VariableDeclarations) param;
                            return varDecl.getTypeExpression() != null &&
                                   varDecl.getTypeExpression().toString().contains(typeName);
                        }
                        return false;
                    });
            }
        };
    }
}
