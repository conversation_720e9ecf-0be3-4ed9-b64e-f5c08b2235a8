/*
 * Copyright 2024 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.phodal.rewrite.java8to17;

import lombok.EqualsAndHashCode;
import lombok.Value;
import org.openrewrite.*;
import org.openrewrite.java.JavaIsoVisitor;
import org.openrewrite.java.MethodMatcher;
import org.openrewrite.java.tree.*;
import org.openrewrite.marker.Markers;

import java.util.Collections;
import java.util.List;

@EqualsAndHashCode(callSuper = false)
public class FixJava17Compatibility extends Recipe {

    // Method matchers for deprecated APIs
    private static final MethodMatcher THREAD_STOP_MATCHER = 
        new MethodMatcher("java.lang.Thread stop()");
    private static final MethodMatcher THREAD_SUSPEND_MATCHER = 
        new MethodMatcher("java.lang.Thread suspend()");
    private static final MethodMatcher THREAD_RESUME_MATCHER = 
        new MethodMatcher("java.lang.Thread resume()");
    private static final MethodMatcher FINALIZE_MATCHER = 
        new MethodMatcher("java.lang.Object finalize()");

    @Override
    public String getDisplayName() {
        return "Fix Java 17 compatibility issues";
    }

    @Override
    public String getDescription() {
        return "Fix common Java 17 compatibility issues including deprecated APIs, " +
               "removed methods, and updated security manager usage.";
    }

    @Override
    public TreeVisitor<?, ExecutionContext> getVisitor() {
        return new JavaIsoVisitor<ExecutionContext>() {
            
            @Override
            public J.MethodInvocation visitMethodInvocation(J.MethodInvocation method, ExecutionContext ctx) {
                J.MethodInvocation m = super.visitMethodInvocation(method, ctx);
                
                // Replace deprecated Thread methods
                if (THREAD_STOP_MATCHER.matches(m)) {
                    return replaceWithComment(m, "Thread.stop() is deprecated and removed in Java 17. " +
                                                "Consider using interrupt() or a volatile boolean flag.");
                }
                
                if (THREAD_SUSPEND_MATCHER.matches(m) || THREAD_RESUME_MATCHER.matches(m)) {
                    return replaceWithComment(m, "Thread.suspend() and resume() are deprecated and removed. " +
                                                "Use wait()/notify() or other synchronization mechanisms.");
                }
                
                return m;
            }
            
            @Override
            public J.MethodDeclaration visitMethodDeclaration(J.MethodDeclaration method, ExecutionContext ctx) {
                J.MethodDeclaration m = super.visitMethodDeclaration(method, ctx);

                // Handle finalize() method
                if (m.getSimpleName().equals("finalize") &&
                    m.getParameters().isEmpty() &&
                    m.getReturnTypeExpression() != null &&
                    m.getReturnTypeExpression().toString().equals("void")) {
                    // Add comment about finalize() deprecation
                    String comment = "finalize() is deprecated in Java 9 and subject to removal. " +
                                   "Consider using try-with-resources or explicit cleanup methods.";
                    return m.withComments(Collections.singletonList(
                        new TextComment(true, comment, "\n", Markers.EMPTY)));
                }

                return m;
            }
            
            @Override
            public J.Import visitImport(J.Import anImport, ExecutionContext ctx) {
                J.Import i = super.visitImport(anImport, ctx);
                
                // Remove imports for removed packages
                String importName = i.getQualid().printTrimmed();
                
                // Handle removed sun.* packages
                if (importName.startsWith("sun.misc.") || 
                    importName.startsWith("sun.security.") ||
                    importName.startsWith("com.sun.image.codec.jpeg")) {
                    // Add comment about removed package
                    String comment = "Package " + importName + " is removed in Java 17. " +
                                   "Consider using standard APIs or third-party libraries.";
                    return i.withComments(Collections.singletonList(
                        new TextComment(true, comment, "\n", Markers.EMPTY)));
                }
                
                return i;
            }
            
            @Override
            public J.FieldAccess visitFieldAccess(J.FieldAccess fieldAccess, ExecutionContext ctx) {
                J.FieldAccess f = super.visitFieldAccess(fieldAccess, ctx);
                
                // Handle SecurityManager related field access
                if (f.getTarget() instanceof J.Identifier) {
                    J.Identifier target = (J.Identifier) f.getTarget();
                    if ("System".equals(target.getSimpleName()) && 
                        "securityManager".equals(f.getSimpleName())) {
                        // Add comment about SecurityManager deprecation
                        String comment = "SecurityManager is deprecated for removal in Java 17. " +
                                       "Consider alternative security mechanisms.";
                        return f.withComments(Collections.singletonList(
                            new TextComment(true, comment, "\n", Markers.EMPTY)));
                    }
                }
                
                return f;
            }
            
            private J.MethodInvocation replaceWithComment(J.MethodInvocation method, String comment) {
                // For now, we'll add a comment. In a real implementation, you might want to
                // replace the method call with alternative code or remove it entirely.
                return method.withComments(Collections.singletonList(
                    new TextComment(true, comment, "\n", Markers.EMPTY)));
            }
        };
    }
}
