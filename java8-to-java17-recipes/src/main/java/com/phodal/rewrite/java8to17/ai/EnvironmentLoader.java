package com.phodal.rewrite.java8to17.ai;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

/**
 * Utility class for loading environment variables from .env file
 */
@Slf4j
public class EnvironmentLoader {
    
    private static final Map<String, String> envVariables = new HashMap<>();
    private static boolean loaded = false;
    
    /**
     * Load environment variables from .env file
     */
    public static void loadEnvironment() {
        if (loaded) {
            return;
        }

        // Try to find .env file in current directory or parent directories
        Path envFile = findEnvFile();
        if (envFile == null) {
            log.info("No .env file found, using system environment variables only");
            loaded = true;
            return;
        }
        
        try (BufferedReader reader = new BufferedReader(new FileReader(envFile.toFile()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                
                // Skip empty lines and comments
                if (line.isEmpty() || line.startsWith("#")) {
                    continue;
                }
                
                // Parse key=value pairs
                int equalIndex = line.indexOf('=');
                if (equalIndex > 0) {
                    String key = line.substring(0, equalIndex).trim();
                    String value = line.substring(equalIndex + 1).trim();
                    
                    // Remove quotes if present
                    if (value.startsWith("\"") && value.endsWith("\"")) {
                        value = value.substring(1, value.length() - 1);
                    }
                    
                    envVariables.put(key, value);
                    log.debug("Loaded environment variable: {}", key);
                }
            }
            
            log.info("Loaded {} environment variables from .env file", envVariables.size());
            loaded = true;
            
        } catch (IOException e) {
            log.error("Error reading .env file: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Get environment variable value
     * First checks .env file, then system environment
     */
    public static String getEnv(String key) {
        loadEnvironment();
        
        // First check .env file
        String value = envVariables.get(key);
        if (value != null) {
            return value;
        }
        
        // Fall back to system environment
        return System.getenv(key);
    }
    
    /**
     * Get environment variable with default value
     */
    public static String getEnv(String key, String defaultValue) {
        String value = getEnv(key);
        return value != null ? value : defaultValue;
    }
    
    /**
     * Check if environment variable exists
     */
    public static boolean hasEnv(String key) {
        return getEnv(key) != null;
    }

    /**
     * Find .env file in current directory or parent directories
     */
    private static Path findEnvFile() {
        Path currentDir = Paths.get("").toAbsolutePath();

        // Check current directory and up to 3 parent directories
        for (int i = 0; i < 4; i++) {
            Path envFile = currentDir.resolve(".env");
            if (Files.exists(envFile)) {
                log.debug("Found .env file at: {}", envFile);
                return envFile;
            }

            Path parent = currentDir.getParent();
            if (parent == null) {
                break;
            }
            currentDir = parent;
        }

        return null;
    }
}
