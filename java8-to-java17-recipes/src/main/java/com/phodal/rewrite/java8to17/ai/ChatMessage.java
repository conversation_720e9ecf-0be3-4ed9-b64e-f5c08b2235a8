package com.phodal.rewrite.java8to17.ai;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Represents a chat message with role and content
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChatMessage {
    
    /**
     * The role of the message sender (system, user, assistant)
     */
    private String role;
    
    /**
     * The content of the message
     */
    private String content;
    
    /**
     * Create a system message
     */
    public static ChatMessage system(String content) {
        return new ChatMessage("system", content);
    }
    
    /**
     * Create a user message
     */
    public static ChatMessage user(String content) {
        return new ChatMessage("user", content);
    }
    
    /**
     * Create an assistant message
     */
    public static ChatMessage assistant(String content) {
        return new ChatMessage("assistant", content);
    }
}
