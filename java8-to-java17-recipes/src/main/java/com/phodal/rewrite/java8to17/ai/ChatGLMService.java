package com.phodal.rewrite.java8to17.ai;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * ChatGLM specific AI service implementation
 * Uses direct HTTP calls to ChatGLM API
 */
@Slf4j
public class ChatGLMService implements AIService {
    
    private final String apiKey;
    private final String baseUrl;
    private final String model;
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;
    
    public ChatGLMService(AIServiceConfig config) {
        this.apiKey = config.getApiKey();
        this.baseUrl = config.getBaseUrl();
        this.model = config.getModel();
        this.objectMapper = new ObjectMapper();
        
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(config.getTimeoutSeconds(), TimeUnit.SECONDS)
                .readTimeout(config.getTimeoutSeconds(), TimeUnit.SECONDS)
                .writeTimeout(config.getTimeoutSeconds(), TimeUnit.SECONDS)
                .build();
    }
    
    @Override
    public String sendMessage(String message) {
        try {
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", model);
            requestBody.put("messages", new Object[]{
                createMessage("user", message)
            });
            requestBody.put("temperature", 0.7);
            requestBody.put("max_tokens", 1000);
            
            return sendRequest(requestBody);
            
        } catch (Exception e) {
            log.error("Error sending message to ChatGLM: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to send message to ChatGLM: " + e.getMessage(), e);
        }
    }
    
    @Override
    public String sendConversation(List<ChatMessage> messages) {
        try {
            Object[] messageArray = messages.stream()
                    .map(msg -> createMessage(msg.getRole(), msg.getContent()))
                    .toArray();
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", model);
            requestBody.put("messages", messageArray);
            requestBody.put("temperature", 0.7);
            requestBody.put("max_tokens", 1000);
            
            return sendRequest(requestBody);
            
        } catch (Exception e) {
            log.error("Error sending conversation to ChatGLM: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to send conversation to ChatGLM: " + e.getMessage(), e);
        }
    }
    
    @Override
    public String getProviderName() {
        return "ChatGLM";
    }
    
    @Override
    public boolean isAvailable() {
        try {
            String response = sendMessage("Hello");
            return response != null && !response.trim().isEmpty();
        } catch (Exception e) {
            log.warn("ChatGLM service is not available: {}", e.getMessage());
            return false;
        }
    }
    
    private Map<String, String> createMessage(String role, String content) {
        Map<String, String> message = new HashMap<>();
        message.put("role", role);
        message.put("content", content);
        return message;
    }
    
    private String sendRequest(Map<String, Object> requestBody) throws IOException {
        String jsonBody = objectMapper.writeValueAsString(requestBody);
        
        RequestBody body = RequestBody.create(
                jsonBody,
                MediaType.parse("application/json; charset=utf-8")
        );
        
        Request request = new Request.Builder()
                .url(baseUrl + "/chat/completions")
                .post(body)
                .addHeader("Authorization", "Bearer " + apiKey)
                .addHeader("Content-Type", "application/json")
                .build();
        
        log.debug("Sending request to ChatGLM: {}", request.url());
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                log.error("ChatGLM API error: {} - {}", response.code(), errorBody);
                throw new IOException("ChatGLM API error: " + response.code() + " - " + errorBody);
            }
            
            String responseBody = response.body().string();
            log.debug("ChatGLM response: {}", responseBody);
            
            return parseResponse(responseBody);
        }
    }
    
    private String parseResponse(String responseBody) throws IOException {
        try {
            JsonNode root = objectMapper.readTree(responseBody);
            JsonNode choices = root.get("choices");
            
            if (choices != null && choices.isArray() && choices.size() > 0) {
                JsonNode firstChoice = choices.get(0);
                JsonNode message = firstChoice.get("message");
                if (message != null) {
                    JsonNode content = message.get("content");
                    if (content != null) {
                        return content.asText();
                    }
                }
            }
            
            log.warn("Unexpected response format from ChatGLM: {}", responseBody);
            return "No response content found";
            
        } catch (Exception e) {
            log.error("Error parsing ChatGLM response: {}", e.getMessage(), e);
            throw new IOException("Failed to parse ChatGLM response: " + e.getMessage(), e);
        }
    }
}
