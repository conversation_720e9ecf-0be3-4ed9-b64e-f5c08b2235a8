package com.phodal.rewrite.java8to17.ai;

import java.util.List;

/**
 * AI Service interface for interacting with various AI providers
 * Supports ChatGLM, DeepSeek, and other OpenAI-compatible APIs
 */
public interface AIService {
    
    /**
     * Send a single message to the AI service and get a response
     * 
     * @param message The message to send
     * @return The AI response
     */
    String sendMessage(String message);
    
    /**
     * Send a conversation with multiple messages to the AI service
     * 
     * @param messages List of messages in the conversation
     * @return The AI response
     */
    String sendConversation(List<ChatMessage> messages);
    
    /**
     * Get the name of the AI service provider
     * 
     * @return Service provider name
     */
    String getProviderName();
    
    /**
     * Check if the service is available and properly configured
     * 
     * @return true if service is available, false otherwise
     */
    boolean isAvailable();
}
