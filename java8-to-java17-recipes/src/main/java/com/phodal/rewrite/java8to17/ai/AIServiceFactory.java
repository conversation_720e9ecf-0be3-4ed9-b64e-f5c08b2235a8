package com.phodal.rewrite.java8to17.ai;

import lombok.extern.slf4j.Slf4j;

/**
 * Factory class for creating AI service instances
 */
@Slf4j
public class AIServiceFactory {
    
    /**
     * Create ChatGLM service instance
     */
    public static AIService createChatGLMService(String apiKey) {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            throw new IllegalArgumentException("ChatGLM API key is required");
        }

        AIServiceConfig config = AIServiceConfig.forChatGLM(apiKey);
        return new ChatGLMService(config);
    }
    
    /**
     * Create DeepSeek service instance
     */
    public static AIService createDeepSeekService(String apiKey) {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            throw new IllegalArgumentException("DeepSeek API key is required");
        }
        
        AIServiceConfig config = AIServiceConfig.forDeepSeek(apiKey);
        return new OpenAICompatibleService(config, "DeepSeek");
    }
    
    /**
     * Create OpenAI service instance
     */
    public static AIService createOpenAIService(String apiKey) {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            throw new IllegalArgumentException("OpenAI API key is required");
        }
        
        AIServiceConfig config = AIServiceConfig.forOpenAI(apiKey);
        return new OpenAICompatibleService(config, "OpenAI");
    }
    
    /**
     * Create service instance with custom configuration
     */
    public static AIService createCustomService(AIServiceConfig config, String providerName) {
        if (config == null) {
            throw new IllegalArgumentException("AI service config is required");
        }
        if (providerName == null || providerName.trim().isEmpty()) {
            throw new IllegalArgumentException("Provider name is required");
        }
        
        return new OpenAICompatibleService(config, providerName);
    }
    
    /**
     * Create service from environment variables
     * Checks for GLM_API_KEY and DEEPSEEK_TOKEN environment variables
     */
    public static AIService createFromEnvironment() {
        // Load environment variables from .env file first
        EnvironmentLoader.loadEnvironment();

        String glmApiKey = EnvironmentLoader.getEnv("GLM_API_KEY");
        String deepSeekToken = EnvironmentLoader.getEnv("DEEPSEEK_TOKEN");
        String openAiKey = EnvironmentLoader.getEnv("OPENAI_API_KEY");
        
        if (glmApiKey != null && !glmApiKey.trim().isEmpty()) {
            log.info("Creating ChatGLM service from environment");
            return createChatGLMService(glmApiKey);
        } else if (deepSeekToken != null && !deepSeekToken.trim().isEmpty()) {
            log.info("Creating DeepSeek service from environment");
            return createDeepSeekService(deepSeekToken);
        } else if (openAiKey != null && !openAiKey.trim().isEmpty()) {
            log.info("Creating OpenAI service from environment");
            return createOpenAIService(openAiKey);
        } else {
            throw new IllegalStateException(
                "No AI service API key found in environment variables. " +
                "Please set GLM_API_KEY, DEEPSEEK_TOKEN, or OPENAI_API_KEY"
            );
        }
    }
}
