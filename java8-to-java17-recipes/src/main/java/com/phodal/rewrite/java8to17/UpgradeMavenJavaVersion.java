/*
 * Copyright 2024 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.phodal.rewrite.java8to17;

import lombok.EqualsAndHashCode;
import lombok.Value;
import org.openrewrite.*;
import org.openrewrite.maven.MavenIsoVisitor;
import org.openrewrite.maven.tree.ResolvedPom;
import org.openrewrite.xml.tree.Xml;

import java.util.Optional;

@EqualsAndHashCode(callSuper = false)
public class UpgradeMavenJavaVersion extends Recipe {

    @Option(displayName = "Java version",
            description = "The Java version to upgrade to.",
            example = "17")
    String javaVersion;

    public UpgradeMavenJavaVersion() {
        this.javaVersion = "17";
    }

    public UpgradeMavenJavaVersion(String javaVersion) {
        this.javaVersion = javaVersion;
    }

    @Override
    public String getDisplayName() {
        return "Upgrade Maven Java version";
    }

    @Override
    public String getDescription() {
        return "Upgrade the Maven Java version from 1.8 to " + javaVersion + ". " +
               "This recipe updates maven.compiler.source, maven.compiler.target properties " +
               "and maven-compiler-plugin configuration.";
    }

    @Override
    public TreeVisitor<?, ExecutionContext> getVisitor() {
        return new MavenIsoVisitor<ExecutionContext>() {
            @Override
            public Xml.Tag visitTag(Xml.Tag tag, ExecutionContext ctx) {
                Xml.Tag t = super.visitTag(tag, ctx);

                if (isPropertyTag()) {
                    String propertyName = tag.getName();

                    // Update maven.compiler.source and maven.compiler.target
                    if ("maven.compiler.source".equals(propertyName) ||
                        "maven.compiler.target".equals(propertyName)) {
                        Optional<String> currentValue = tag.getValue();
                        if (currentValue.isPresent() &&
                            (currentValue.get().equals("1.8") || currentValue.get().equals("8"))) {
                            return t.withValue(javaVersion);
                        }
                    }

                    // Update java.version property (commonly used in Spring Boot projects)
                    if ("java.version".equals(propertyName)) {
                        Optional<String> currentValue = tag.getValue();
                        if (currentValue.isPresent() &&
                            (currentValue.get().equals("1.8") || currentValue.get().equals("8"))) {
                            return t.withValue(javaVersion);
                        }
                    }
                }

                // Update maven-compiler-plugin configuration
                if (isPluginTag("org.apache.maven.plugins", "maven-compiler-plugin")) {
                    return updateCompilerPluginConfiguration(t);
                }

                return t;
            }

            private Xml.Tag updateCompilerPluginConfiguration(Xml.Tag pluginTag) {
                boolean hasChanges = false;
                for (Object content : pluginTag.getContent()) {
                    if (content instanceof Xml.Tag) {
                        Xml.Tag childTag = (Xml.Tag) content;
                        if ("configuration".equals(childTag.getName())) {
                            for (Object configContent : childTag.getContent()) {
                                if (configContent instanceof Xml.Tag) {
                                    Xml.Tag configChildTag = (Xml.Tag) configContent;
                                    if (("source".equals(configChildTag.getName()) ||
                                         "target".equals(configChildTag.getName()) ||
                                         "release".equals(configChildTag.getName()))) {
                                        Optional<String> currentValue = configChildTag.getValue();
                                        if (currentValue.isPresent() &&
                                            (currentValue.get().equals("1.8") ||
                                             currentValue.get().equals("8"))) {
                                            hasChanges = true;
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                if (!hasChanges) {
                    return pluginTag;
                }

                return pluginTag.withContent(pluginTag.getContent().stream()
                    .map(content -> {
                        if (content instanceof Xml.Tag) {
                            Xml.Tag childTag = (Xml.Tag) content;
                            if ("configuration".equals(childTag.getName())) {
                                return updateConfigurationTag(childTag);
                            }
                        }
                        return content;
                    })
                    .collect(java.util.stream.Collectors.toList()));
            }

            private Xml.Tag updateConfigurationTag(Xml.Tag configTag) {
                return configTag.withContent(configTag.getContent().stream()
                    .map(content -> {
                        if (content instanceof Xml.Tag) {
                            Xml.Tag childTag = (Xml.Tag) content;
                            if ("source".equals(childTag.getName()) || "target".equals(childTag.getName())) {
                                Optional<String> currentValue = childTag.getValue();
                                if (currentValue.isPresent() &&
                                    (currentValue.get().equals("1.8") || currentValue.get().equals("8"))) {
                                    return childTag.withValue(javaVersion);
                                }
                            }
                            if ("release".equals(childTag.getName())) {
                                Optional<String> currentValue = childTag.getValue();
                                if (currentValue.isPresent() &&
                                    (currentValue.get().equals("8"))) {
                                    return childTag.withValue(javaVersion);
                                }
                            }
                        }
                        return content;
                    })
                    .collect(java.util.stream.Collectors.toList()));
            }
        };
    }
}
