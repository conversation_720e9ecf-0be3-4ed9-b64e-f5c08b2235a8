package com.phodal.rewrite.java8to17.cli;

import com.phodal.rewrite.java8to17.*;
import org.openrewrite.*;
import org.openrewrite.config.Environment;
import org.openrewrite.java.JavaParser;
import org.openrewrite.maven.MavenParser;
import org.openrewrite.xml.XmlParser;

import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.*;
import java.util.stream.Collectors;

/**
 * CLI tool for migrating Java 8 projects to Java 17
 * This tool runs independently without requiring rewrite.yml configuration
 */
public class Java8ToJava17MigrationCLI {

    public static void main(String[] args) {
        try {
            CLIArgumentParser.CLIOptions options = CLIArgumentParser.parseArguments(args);
            if (options == null) {
                CLIArgumentParser.printUsage();
                System.exit(1);
            }

            System.out.println("Starting Java 8 to Java 17 migration...");
            System.out.println("Source directory: " + options.getSourceDir());
            System.out.println("Target Spring Boot version: " + options.getSpringBootVersion());
            System.out.println("Target Java version: " + options.getJavaVersion());

            if (options.isDryRun()) {
                System.out.println("DRY RUN MODE - No files will be modified");
            }

            if (options.getOutputDir() != null) {
                System.out.println("Output directory: " + options.getOutputDir());
            }

            Java8ToJava17MigrationCLI cli = new Java8ToJava17MigrationCLI();
            cli.runMigration(options);

            System.out.println("Migration completed successfully!");

        } catch (Exception e) {
            System.err.println("Migration failed: " + e.getMessage());
            if (args.length > 0 && (Arrays.asList(args).contains("-v") || Arrays.asList(args).contains("--verbose"))) {
                e.printStackTrace();
            }
            System.exit(1);
        }
    }

    
    public void runMigration(CLIArgumentParser.CLIOptions options) throws Exception {
        // Find source files
        List<Path> sourceFiles = findSourceFiles(options.getSourceDir());
        System.out.println("Found " + sourceFiles.size() + " source files to process");

        // Parse source files
        List<SourceFile> sources = parseSourceFiles(sourceFiles, options.getSourceDir());
        System.out.println("Parsed " + sources.size() + " source files");

        // Create migration recipes
        List<Recipe> recipes = createMigrationRecipes(options);

        // Execute migration
        for (Recipe recipe : recipes) {
            System.out.println("Executing recipe: " + recipe.getDisplayName());
            sources = executeRecipe(recipe, sources);
        }

        // Write results back to files
        if (!options.isDryRun()) {
            Path outputDir = options.getOutputDir() != null ?
                Paths.get(options.getOutputDir()) : options.getSourceDir();
            writeResults(sources, outputDir);
        } else {
            System.out.println("DRY RUN: Would modify " + sources.size() + " files");
        }

        System.out.println("Migration recipes applied successfully");
    }
    
    private List<Recipe> createMigrationRecipes(CLIArgumentParser.CLIOptions options) {
        List<Recipe> recipes = new ArrayList<>();

        // Add all migration recipes in order
        recipes.add(new UpgradeMavenJavaVersion(options.getJavaVersion()));
        recipes.add(new UpgradeGradleJavaVersion(options.getJavaVersion()));
        recipes.add(new UpgradeSpringBootForJava17(options.getSpringBootVersion()));
        recipes.add(new FixJava17Compatibility());
        recipes.add(new FixSpringBootJava17Compatibility());
        recipes.add(new RemoveDeprecatedDependencies());

        return recipes;
    }
    
    private List<Path> findSourceFiles(Path sourceDir) throws IOException {
        List<Path> sourceFiles = new ArrayList<>();
        
        Files.walkFileTree(sourceDir, new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                String fileName = file.getFileName().toString();
                if (fileName.endsWith(".java") || 
                    fileName.endsWith(".xml") || 
                    fileName.equals("pom.xml") ||
                    fileName.endsWith(".gradle") ||
                    fileName.equals("build.gradle")) {
                    sourceFiles.add(file);
                }
                return FileVisitResult.CONTINUE;
            }
        });
        
        return sourceFiles;
    }
    
    private List<SourceFile> parseSourceFiles(List<Path> sourceFiles, Path baseDir) throws Exception {
        List<SourceFile> sources = new ArrayList<>();
        
        // Group files by type
        List<Path> javaFiles = sourceFiles.stream()
            .filter(p -> p.toString().endsWith(".java"))
            .collect(Collectors.toList());
            
        List<Path> xmlFiles = sourceFiles.stream()
            .filter(p -> p.toString().endsWith(".xml"))
            .collect(Collectors.toList());
            
        List<Path> gradleFiles = sourceFiles.stream()
            .filter(p -> p.toString().endsWith(".gradle") || p.getFileName().toString().equals("build.gradle"))
            .collect(Collectors.toList());
        
        // Parse Java files
        if (!javaFiles.isEmpty()) {
            JavaParser javaParser = JavaParser.fromJavaVersion()
                .classpath(JavaParser.runtimeClasspath())
                .build();
            sources.addAll(javaParser.parse(javaFiles, baseDir, new InMemoryExecutionContext()).collect(Collectors.toList()));
        }

        // Parse XML files (including pom.xml)
        if (!xmlFiles.isEmpty()) {
            sources.addAll(XmlParser.builder().build().parse(xmlFiles, baseDir, new InMemoryExecutionContext()).collect(Collectors.toList()));
        }

        // Parse Maven pom.xml files specifically
        List<Path> pomFiles = xmlFiles.stream()
            .filter(p -> p.getFileName().toString().equals("pom.xml"))
            .collect(Collectors.toList());

        if (!pomFiles.isEmpty()) {
            MavenParser mavenParser = MavenParser.builder().build();
            sources.addAll(mavenParser.parse(pomFiles, baseDir, new InMemoryExecutionContext()).collect(Collectors.toList()));
        }
        
        return sources;
    }
    
    private List<SourceFile> executeRecipe(Recipe recipe, List<SourceFile> sources) {
        ExecutionContext ctx = new InMemoryExecutionContext();

        // Apply recipe visitor to each source file
        List<SourceFile> modifiedSources = new ArrayList<>();
        TreeVisitor<?, ExecutionContext> visitor = recipe.getVisitor();

        for (SourceFile source : sources) {
            SourceFile modified = (SourceFile) visitor.visit(source, ctx);
            if (modified != null) {
                modifiedSources.add(modified);
            } else {
                modifiedSources.add(source);
            }
        }

        return modifiedSources;
    }
    
    private void writeResults(List<SourceFile> sources, Path baseDir) throws IOException {
        for (SourceFile source : sources) {
            Path sourcePath = baseDir.resolve(source.getSourcePath());
            
            // Create parent directories if they don't exist
            Files.createDirectories(sourcePath.getParent());
            
            // Write the modified content
            Files.write(sourcePath, source.printAll().getBytes());
        }
    }

}
