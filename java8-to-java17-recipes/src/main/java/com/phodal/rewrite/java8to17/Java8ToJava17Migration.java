/*
 * Copyright 2024 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.phodal.rewrite.java8to17;

import lombok.EqualsAndHashCode;
import lombok.Value;
import org.openrewrite.*;
import org.openrewrite.maven.UpgradeDependencyVersion;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;

@EqualsAndHashCode(callSuper = false)
public class Java8ToJava17Migration extends Recipe {

    @Option(displayName = "Target Spring Boot version",
            description = "The Spring Boot version to upgrade to (must be compatible with Java 17).",
            example = "2.7.18")
    String springBootVersion;

    public Java8ToJava17Migration() {
        this.springBootVersion = "2.7.18";
    }

    public Java8ToJava17Migration(String springBootVersion) {
        this.springBootVersion = springBootVersion;
    }

    @Override
    public String getDisplayName() {
        return "Migrate from Java 8 to Java 17";
    }

    @Override
    public String getDescription() {
        return "Comprehensive migration from Java 8 to Java 17 including:\n" +
               "- Upgrade Java compiler version to 17\n" +
               "- Upgrade Spring Boot to Java 17 compatible version\n" +
               "- Fix Java 17 compatibility issues\n" +
               "- Update deprecated Spring Boot configurations\n" +
               "- Remove or update deprecated dependencies";
    }

    @Override
    public Duration getEstimatedEffortPerOccurrence() {
        return Duration.ofMinutes(30);
    }

    @Override
    public List<Recipe> getRecipeList() {
        return Arrays.asList(
            // Step 1: Upgrade Java version in build files
            new UpgradeMavenJavaVersion("17"),
            new UpgradeGradleJavaVersion("17"),
            
            // Step 2: Upgrade Spring Boot version
            new UpgradeSpringBootForJava17(springBootVersion),
            
            // Step 3: Update common dependencies that need upgrading for Java 17
            new UpgradeDependencyVersion(
                "org.apache.tomcat.embed",
                "tomcat-embed-*",
                "9.0.x",
                null,
                null,
                null
            ),
            
            // Step 4: Fix Java 17 compatibility issues
            new FixJava17Compatibility(),
            new FixSpringBootJava17Compatibility(),
            
            // Step 5: Update logging dependencies if needed
            new UpgradeDependencyVersion(
                "ch.qos.logback",
                "logback-*",
                "1.2.x",
                null,
                null,
                null
            ),
            
            // Step 6: Remove deprecated spring-loaded dependency
            new RemoveDeprecatedDependencies()
        );
    }
}
