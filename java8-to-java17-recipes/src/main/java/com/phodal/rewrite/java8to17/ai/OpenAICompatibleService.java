package com.phodal.rewrite.java8to17.ai;

import com.theokanning.openai.completion.chat.ChatCompletionRequest;
import com.theokanning.openai.completion.chat.ChatCompletionResult;
import com.theokanning.openai.service.OpenAiService;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * OpenAI-compatible AI service implementation
 * Works with ChatGLM, DeepSeek, and other OpenAI-compatible APIs
 */
@Slf4j
public class OpenAICompatibleService implements AIService {
    
    private final OpenAiService openAiService;
    private final AIServiceConfig config;
    private final String providerName;
    
    public OpenAICompatibleService(AIServiceConfig config, String providerName) {
        this.config = config;
        this.providerName = providerName;
        
        // Create OpenAI service with custom configuration
        this.openAiService = new OpenAiService(
            config.getApiKey(),
            Duration.ofSeconds(config.getTimeoutSeconds())
        );
        
        // Set custom base URL if provided
        if (config.getBaseUrl() != null && !config.getBaseUrl().isEmpty()) {
            // Note: The OpenAI Java SDK might need custom configuration for different base URLs
            // This is a simplified implementation
            log.info("Using custom base URL: {}", config.getBaseUrl());
        }
    }
    
    @Override
    public String sendMessage(String message) {
        return sendConversation(Arrays.asList(ChatMessage.user(message)));
    }

    @Override
    public String sendConversation(List<ChatMessage> messages) {
        try {
            // Convert our ChatMessage to OpenAI ChatMessage
            List<com.theokanning.openai.completion.chat.ChatMessage> openAIMessages = messages.stream()
                    .map(msg -> new com.theokanning.openai.completion.chat.ChatMessage(msg.getRole(), msg.getContent()))
                    .collect(Collectors.toList());
            
            ChatCompletionRequest request = ChatCompletionRequest.builder()
                    .model(config.getModel())
                    .messages(openAIMessages)
                    .maxTokens(config.getMaxTokens())
                    .temperature(config.getTemperature())
                    .build();
            
            ChatCompletionResult result = openAiService.createChatCompletion(request);
            
            if (result.getChoices() != null && !result.getChoices().isEmpty()) {
                return result.getChoices().get(0).getMessage().getContent();
            } else {
                log.warn("No response received from AI service");
                return "No response received";
            }
            
        } catch (Exception e) {
            log.error("Error calling AI service: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to get response from AI service: " + e.getMessage(), e);
        }
    }
    
    @Override
    public String getProviderName() {
        return providerName;
    }
    
    @Override
    public boolean isAvailable() {
        try {
            // Simple availability check by sending a test message
            String response = sendMessage("Hello");
            return response != null && !response.trim().isEmpty();
        } catch (Exception e) {
            log.warn("AI service {} is not available: {}", providerName, e.getMessage());
            return false;
        }
    }
}
