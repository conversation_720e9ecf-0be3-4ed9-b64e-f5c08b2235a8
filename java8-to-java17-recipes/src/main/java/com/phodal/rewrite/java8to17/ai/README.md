# AIService 模块

这个模块提供了一个统一的接口来调用各种 AI 服务，包括 ChatGLM、DeepSeek 和其他 OpenAI 兼容的 API。

## 功能特性

- 支持多种 AI 服务提供商（ChatGLM、DeepSeek、OpenAI）
- 统一的接口设计，易于扩展
- 自动从环境变量或 .env 文件加载配置
- 支持单条消息和多轮对话
- 内置错误处理和日志记录

## 快速开始

### 1. 配置环境变量

创建 `.env` 文件在项目根目录：

```env
GLM_API_KEY="your-chatglm-api-key"
DEEPSEEK_TOKEN="your-deepseek-token"
OPENAI_API_KEY="your-openai-api-key"
```

**注意**: `.env` 文件已经被添加到 `.gitignore` 中，不会被提交到代码库。您可以参考 `.env.example` 文件作为模板。

### 2. 基本使用

```java
import com.phodal.rewrite.java8to17.ai.*;

// 从环境变量自动创建服务
AIService aiService = AIServiceFactory.createFromEnvironment();

// 发送简单消息
String response = aiService.sendMessage("Hello, can you help me with Java?");
System.out.println(response);

// 多轮对话
List<ChatMessage> conversation = Arrays.asList(
    ChatMessage.system("You are a Java expert."),
    ChatMessage.user("How to migrate from Java 8 to Java 17?")
);
String conversationResponse = aiService.sendConversation(conversation);
System.out.println(conversationResponse);
```

### 3. 指定特定服务

```java
// 使用 ChatGLM
AIService chatGLM = AIServiceFactory.createChatGLMService("your-api-key");

// 使用 DeepSeek
AIService deepSeek = AIServiceFactory.createDeepSeekService("your-token");

// 使用 OpenAI
AIService openAI = AIServiceFactory.createOpenAIService("your-api-key");
```

### 4. 自定义配置

```java
AIServiceConfig config = AIServiceConfig.builder()
    .apiKey("your-api-key")
    .baseUrl("https://your-custom-endpoint.com")
    .model("your-model")
    .maxTokens(2000)
    .temperature(0.8)
    .timeoutSeconds(60)
    .build();

AIService customService = AIServiceFactory.createCustomService(config, "CustomProvider");
```

## API 参考

### AIService 接口

- `String sendMessage(String message)` - 发送单条消息
- `String sendConversation(List<ChatMessage> messages)` - 发送多轮对话
- `String getProviderName()` - 获取服务提供商名称
- `boolean isAvailable()` - 检查服务是否可用

### ChatMessage 类

- `ChatMessage.system(String content)` - 创建系统消息
- `ChatMessage.user(String content)` - 创建用户消息
- `ChatMessage.assistant(String content)` - 创建助手消息

### AIServiceFactory 工厂类

- `createChatGLMService(String apiKey)` - 创建 ChatGLM 服务
- `createDeepSeekService(String apiKey)` - 创建 DeepSeek 服务
- `createOpenAIService(String apiKey)` - 创建 OpenAI 服务
- `createFromEnvironment()` - 从环境变量创建服务
- `createCustomService(AIServiceConfig config, String providerName)` - 创建自定义服务

## 示例用例

### Java 代码分析

```java
public class CodeAnalyzer {
    public static void analyzeCode(String javaCode) {
        AIService aiService = AIServiceFactory.createFromEnvironment();
        
        List<ChatMessage> messages = Arrays.asList(
            ChatMessage.system("You are a Java code analysis expert."),
            ChatMessage.user("Analyze this code and suggest Java 17 improvements:\n" + javaCode)
        );
        
        String analysis = aiService.sendConversation(messages);
        System.out.println("Analysis: " + analysis);
    }
}
```

### 迁移建议生成

```java
public class MigrationHelper {
    public static String generateMigrationPlan(String oldCode) {
        AIService aiService = AIServiceFactory.createFromEnvironment();
        
        String prompt = "Please create a migration plan for this Java 8 code to Java 17:\n" + oldCode;
        return aiService.sendMessage(prompt);
    }
}
```

## 配置说明

### 支持的环境变量

- `GLM_API_KEY` - ChatGLM API 密钥
- `DEEPSEEK_TOKEN` - DeepSeek API 令牌
- `OPENAI_API_KEY` - OpenAI API 密钥

### 默认配置

- **ChatGLM**: 
  - Base URL: `https://open.bigmodel.cn/api/paas/v4`
  - Model: `glm-4`
  
- **DeepSeek**: 
  - Base URL: `https://api.deepseek.com`
  - Model: `deepseek-chat`
  
- **OpenAI**: 
  - Base URL: `https://api.openai.com/v1`
  - Model: `gpt-3.5-turbo`

## 错误处理

所有 API 调用都包含适当的错误处理：

```java
try {
    AIService aiService = AIServiceFactory.createFromEnvironment();
    String response = aiService.sendMessage("Hello");
} catch (IllegalStateException e) {
    // 没有找到 API 密钥
    System.err.println("No API key configured: " + e.getMessage());
} catch (RuntimeException e) {
    // API 调用失败
    System.err.println("API call failed: " + e.getMessage());
}
```

## 测试

运行测试：

```bash
mvn test
```

注意：集成测试需要真实的 API 密钥，默认情况下被禁用。要启用集成测试，请移除测试方法上的 `@Disabled` 注解。

## 依赖

- OpenAI Java SDK
- Jackson (JSON 处理)
- OkHttp (HTTP 客户端)
- Lombok (代码简化)
- SLF4J (日志记录)
