package com.phodal.rewrite.java8to17.ai;

import lombok.Builder;
import lombok.Data;

/**
 * Configuration for AI Service
 */
@Data
@Builder
public class AIServiceConfig {
    
    /**
     * API key for the service
     */
    private String apiKey;
    
    /**
     * Base URL for the API endpoint
     */
    private String baseUrl;
    
    /**
     * Model name to use
     */
    private String model;
    
    /**
     * Maximum tokens for response
     */
    @Builder.Default
    private Integer maxTokens = 1000;
    
    /**
     * Temperature for response randomness (0.0 to 1.0)
     */
    @Builder.Default
    private Double temperature = 0.7;
    
    /**
     * Request timeout in seconds
     */
    @Builder.Default
    private Integer timeoutSeconds = 30;
    
    /**
     * Create config for ChatGLM
     */
    public static AIServiceConfig forChatGLM(String apiKey) {
        return AIServiceConfig.builder()
                .apiKey(apiKey)
                .baseUrl("https://open.bigmodel.cn/api/paas/v4")
                .model("glm-4")
                .build();
    }
    
    /**
     * Create config for DeepSeek
     */
    public static AIServiceConfig forDeepSeek(String apiKey) {
        return AIServiceConfig.builder()
                .apiKey(apiKey)
                .baseUrl("https://api.deepseek.com")
                .model("deepseek-chat")
                .build();
    }
    
    /**
     * Create config for OpenAI
     */
    public static AIServiceConfig forOpenAI(String apiKey) {
        return AIServiceConfig.builder()
                .apiKey(apiKey)
                .baseUrl("https://api.openai.com/v1")
                .model("gpt-3.5-turbo")
                .build();
    }
}
