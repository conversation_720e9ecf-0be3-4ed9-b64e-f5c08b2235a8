package com.phodal.rewrite.java8to17.cli;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

/**
 * Command line argument parser for Java 8 to Java 17 migration CLI
 */
public class CLIArgumentParser {
    
    private static final String DEFAULT_SPRING_BOOT_VERSION = "2.7.18";
    private static final String DEFAULT_JAVA_VERSION = "17";
    private static final List<String> SUPPORTED_JAVA_VERSIONS = Arrays.asList("11", "17", "21");
    
    public static class CLIOptions {
        private Path sourceDir;
        private String springBootVersion = DEFAULT_SPRING_BOOT_VERSION;
        private String javaVersion = DEFAULT_JAVA_VERSION;
        private boolean verbose = false;
        private boolean dryRun = false;
        private boolean skipTests = false;
        private String outputDir = null;
        
        // Getters
        public Path getSourceDir() { return sourceDir; }
        public String getSpringBootVersion() { return springBootVersion; }
        public String getJavaVersion() { return javaVersion; }
        public boolean isVerbose() { return verbose; }
        public boolean isDryRun() { return dryRun; }
        public boolean isSkipTests() { return skipTests; }
        public String getOutputDir() { return outputDir; }
        
        // Setters
        public void setSourceDir(Path sourceDir) { this.sourceDir = sourceDir; }
        public void setSpringBootVersion(String springBootVersion) { this.springBootVersion = springBootVersion; }
        public void setJavaVersion(String javaVersion) { this.javaVersion = javaVersion; }
        public void setVerbose(boolean verbose) { this.verbose = verbose; }
        public void setDryRun(boolean dryRun) { this.dryRun = dryRun; }
        public void setSkipTests(boolean skipTests) { this.skipTests = skipTests; }
        public void setOutputDir(String outputDir) { this.outputDir = outputDir; }
    }
    
    public static CLIOptions parseArguments(String[] args) {
        if (args.length == 0) {
            return null;
        }
        
        CLIOptions options = new CLIOptions();
        
        for (int i = 0; i < args.length; i++) {
            String arg = args[i];
            
            switch (arg) {
                case "-s":
                case "--source":
                    if (i + 1 < args.length) {
                        options.setSourceDir(Paths.get(args[++i]));
                    } else {
                        throw new IllegalArgumentException("Missing value for " + arg);
                    }
                    break;
                    
                case "-sb":
                case "--spring-boot-version":
                    if (i + 1 < args.length) {
                        String version = args[++i];
                        if (isValidSpringBootVersion(version)) {
                            options.setSpringBootVersion(version);
                        } else {
                            throw new IllegalArgumentException("Invalid Spring Boot version: " + version);
                        }
                    } else {
                        throw new IllegalArgumentException("Missing value for " + arg);
                    }
                    break;
                    
                case "-j":
                case "--java-version":
                    if (i + 1 < args.length) {
                        String version = args[++i];
                        if (SUPPORTED_JAVA_VERSIONS.contains(version)) {
                            options.setJavaVersion(version);
                        } else {
                            throw new IllegalArgumentException("Unsupported Java version: " + version + 
                                ". Supported versions: " + SUPPORTED_JAVA_VERSIONS);
                        }
                    } else {
                        throw new IllegalArgumentException("Missing value for " + arg);
                    }
                    break;
                    
                case "-o":
                case "--output":
                    if (i + 1 < args.length) {
                        options.setOutputDir(args[++i]);
                    } else {
                        throw new IllegalArgumentException("Missing value for " + arg);
                    }
                    break;
                    
                case "-v":
                case "--verbose":
                    options.setVerbose(true);
                    break;
                    
                case "-d":
                case "--dry-run":
                    options.setDryRun(true);
                    break;
                    
                case "--skip-tests":
                    options.setSkipTests(true);
                    break;
                    
                case "-h":
                case "--help":
                    return null;
                    
                default:
                    if (!arg.startsWith("-")) {
                        // Treat as source directory if not already set
                        if (options.getSourceDir() == null) {
                            options.setSourceDir(Paths.get(arg));
                        }
                    } else {
                        throw new IllegalArgumentException("Unknown option: " + arg);
                    }
                    break;
            }
        }
        
        // Validate required options
        if (options.getSourceDir() == null) {
            throw new IllegalArgumentException("Source directory is required");
        }
        
        if (!Files.exists(options.getSourceDir())) {
            throw new IllegalArgumentException("Source directory does not exist: " + options.getSourceDir());
        }
        
        if (!Files.isDirectory(options.getSourceDir())) {
            throw new IllegalArgumentException("Source path is not a directory: " + options.getSourceDir());
        }
        
        // Validate output directory if specified
        if (options.getOutputDir() != null) {
            Path outputPath = Paths.get(options.getOutputDir());
            if (Files.exists(outputPath) && !Files.isDirectory(outputPath)) {
                throw new IllegalArgumentException("Output path exists but is not a directory: " + outputPath);
            }
        }
        
        return options;
    }
    
    private static boolean isValidSpringBootVersion(String version) {
        // Basic validation for Spring Boot version format
        if (version == null || version.trim().isEmpty()) {
            return false;
        }
        
        // Check if it matches common Spring Boot version patterns
        return version.matches("^[0-9]+\\.[0-9]+\\.[0-9]+(\\.(RELEASE|SNAPSHOT|M[0-9]+|RC[0-9]+))?$") ||
               version.matches("^[0-9]+\\.[0-9]+\\.[0-9]+$");
    }
    
    public static void printUsage() {
        System.out.println("Java 8 to Java 17 Migration CLI Tool");
        System.out.println("=====================================");
        System.out.println();
        System.out.println("Usage: java -jar java8-to-java17-migration-cli.jar [OPTIONS] <source-directory>");
        System.out.println();
        System.out.println("Required Arguments:");
        System.out.println("  <source-directory>              Path to the Java project to migrate");
        System.out.println();
        System.out.println("Options:");
        System.out.println("  -s, --source <dir>              Source directory to migrate (alternative to positional arg)");
        System.out.println("  -sb, --spring-boot-version <v>  Target Spring Boot version (default: " + DEFAULT_SPRING_BOOT_VERSION + ")");
        System.out.println("  -j, --java-version <v>          Target Java version: " + SUPPORTED_JAVA_VERSIONS + " (default: " + DEFAULT_JAVA_VERSION + ")");
        System.out.println("  -o, --output <dir>              Output directory (default: modify files in place)");
        System.out.println("  -v, --verbose                   Enable verbose output");
        System.out.println("  -d, --dry-run                   Show what would be changed without modifying files");
        System.out.println("  --skip-tests                    Skip processing test files");
        System.out.println("  -h, --help                      Show this help message");
        System.out.println();
        System.out.println("Examples:");
        System.out.println("  # Basic migration");
        System.out.println("  java -jar java8-to-java17-migration-cli.jar /path/to/project");
        System.out.println();
        System.out.println("  # Migrate to specific Spring Boot version");
        System.out.println("  java -jar java8-to-java17-migration-cli.jar -sb 3.0.0 /path/to/project");
        System.out.println();
        System.out.println("  # Dry run to see what would change");
        System.out.println("  java -jar java8-to-java17-migration-cli.jar -d -v /path/to/project");
        System.out.println();
        System.out.println("  # Migrate to Java 21");
        System.out.println("  java -jar java8-to-java17-migration-cli.jar -j 21 /path/to/project");
        System.out.println();
        System.out.println("  # Output to different directory");
        System.out.println("  java -jar java8-to-java17-migration-cli.jar -o /path/to/output /path/to/project");
        System.out.println();
        System.out.println("Migration Features:");
        System.out.println("  • Upgrade Maven/Gradle Java version configuration");
        System.out.println("  • Upgrade Spring Boot to Java 17+ compatible versions");
        System.out.println("  • Fix Java 17 compatibility issues");
        System.out.println("  • Remove deprecated dependencies");
        System.out.println("  • Update build tool configurations");
        System.out.println();
    }
}
