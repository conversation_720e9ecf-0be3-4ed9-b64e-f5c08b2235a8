/*
 * Copyright 2024 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.phodal.rewrite.java8to17;

import lombok.EqualsAndHashCode;
import lombok.Value;
import org.openrewrite.*;
import org.openrewrite.maven.MavenIsoVisitor;
import org.openrewrite.maven.tree.ResolvedDependency;
import org.openrewrite.xml.tree.Xml;

import java.util.Arrays;
import java.util.List;

@EqualsAndHashCode(callSuper = false)
public class RemoveDeprecatedDependencies extends Recipe {

    // List of dependencies that are deprecated or incompatible with Java 17
    private static final List<String> DEPRECATED_DEPENDENCIES = Arrays.asList(
        "org.springframework:springloaded",
        "com.sun.xml.bind:jaxb-impl",
        "javax.xml.bind:jaxb-api",
        "com.sun.xml.ws:jaxws-rt",
        "javax.jws:javax.jws-api"
    );

    @Override
    public String getDisplayName() {
        return "Remove deprecated dependencies";
    }

    @Override
    public String getDescription() {
        return "Remove dependencies that are deprecated or incompatible with Java 17, " +
               "such as spring-loaded, JAXB implementations that are now part of JDK, etc.";
    }

    @Override
    public TreeVisitor<?, ExecutionContext> getVisitor() {
        return new MavenIsoVisitor<ExecutionContext>() {
            @Override
            public Xml.Tag visitTag(Xml.Tag tag, ExecutionContext ctx) {
                Xml.Tag t = super.visitTag(tag, ctx);
                
                if (isDependencyTag()) {
                    ResolvedDependency dependency = findDependency(tag);
                    if (dependency != null) {
                        String dependencyKey = dependency.getGroupId() + ":" + dependency.getArtifactId();
                        
                        if (isDeprecatedDependency(dependencyKey)) {
                            // Mark for removal by returning null
                            // In practice, you might want to add a comment explaining the removal
                            return null;
                        }
                    }
                }
                
                // Remove spring-loaded from spring-boot-maven-plugin dependencies
                if (isPluginTag("org.springframework.boot", "spring-boot-maven-plugin")) {
                    return removeSpringLoadedFromPlugin(t);
                }
                
                return t;
            }
            
            private boolean isDeprecatedDependency(String dependencyKey) {
                return DEPRECATED_DEPENDENCIES.stream()
                    .anyMatch(deprecated -> {
                        if (deprecated.endsWith("*")) {
                            String prefix = deprecated.substring(0, deprecated.length() - 1);
                            return dependencyKey.startsWith(prefix);
                        }
                        return dependencyKey.equals(deprecated);
                    });
            }
            
            private Xml.Tag removeSpringLoadedFromPlugin(Xml.Tag pluginTag) {
                return pluginTag.withContent(pluginTag.getContent().stream()
                    .map(content -> {
                        if (content instanceof Xml.Tag) {
                            Xml.Tag childTag = (Xml.Tag) content;
                            if ("dependencies".equals(childTag.getName())) {
                                return removeSpringLoadedDependencies(childTag);
                            }
                        }
                        return content;
                    })
                    .collect(java.util.stream.Collectors.toList()));
            }
            
            private Xml.Tag removeSpringLoadedDependencies(Xml.Tag dependenciesTag) {
                return dependenciesTag.withContent(dependenciesTag.getContent().stream()
                    .filter(content -> {
                        if (content instanceof Xml.Tag) {
                            Xml.Tag dependencyTag = (Xml.Tag) content;
                            if ("dependency".equals(dependencyTag.getName())) {
                                return !isSpringLoadedDependency(dependencyTag);
                            }
                        }
                        return true;
                    })
                    .collect(java.util.stream.Collectors.toList()));
            }
            
            private boolean isSpringLoadedDependency(Xml.Tag dependencyTag) {
                return dependencyTag.getChildren().stream()
                    .anyMatch(child -> {
                        if (child instanceof Xml.Tag) {
                            Xml.Tag childTag = (Xml.Tag) child;
                            if ("groupId".equals(childTag.getName())) {
                                return "org.springframework".equals(childTag.getValue().orElse(""));
                            }
                            if ("artifactId".equals(childTag.getName())) {
                                return "springloaded".equals(childTag.getValue().orElse(""));
                            }
                        }
                        return false;
                    });
            }
        };
    }
}
