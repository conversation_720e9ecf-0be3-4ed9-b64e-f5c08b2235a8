# Java 8 到 Java 17 升级工具

这个项目基于 OpenRewrite 实现了一套完整的 Java 8 到 Java 17 升级工具，包含自定义 Recipe 和测试验证。

## 项目结构

```
java8-to-java17-recipes/
├── src/main/java/com/phodal/rewrite/java8to17/
│   ├── Java8ToJava17Migration.java          # 主要的组合 Recipe
│   ├── UpgradeMavenJavaVersion.java          # Maven Java 版本升级
│   ├── UpgradeGradleJavaVersion.java         # Gradle Java 版本升级
│   ├── UpgradeSpringBootForJava17.java       # Spring Boot 版本升级
│   ├── FixJava17Compatibility.java          # Java 17 兼容性修复
│   ├── FixSpringBootJava17Compatibility.java # Spring Boot 兼容性修复
│   ├── RemoveDeprecatedDependencies.java    # 移除废弃依赖
│   └── ai/                                  # AI 服务模块
│       ├── AIService.java                   # AI 服务接口
│       ├── ChatGLMService.java              # ChatGLM 实现
│       ├── OpenAICompatibleService.java     # OpenAI 兼容实现
│       ├── AIServiceFactory.java            # 服务工厂
│       ├── AIServiceConfig.java             # 配置类
│       ├── ChatMessage.java                 # 消息类
│       ├── EnvironmentLoader.java           # 环境变量加载器
│       └── README.md                        # AI 模块文档
├── src/main/resources/META-INF/rewrite/
│   └── java8-to-java17.yml                  # Recipe 配置文件
├── src/test/java/                           # 测试用例
├── .env.example                             # 环境变量模板
└── pom.xml                                  # 项目配置
```

## 实现的 Recipe

### 1. UpgradeMavenJavaVersion
- 升级 Maven 项目的 Java 编译器版本
- 更新 `maven.compiler.source` 和 `maven.compiler.target` 属性
- 更新 `java.version` 属性
- 更新 maven-compiler-plugin 配置中的 `<source>`、`<target>` 和 `<release>` 设置

### 2. UpgradeGradleJavaVersion
- 升级 Gradle 项目的 Java 编译器版本
- 更新 `sourceCompatibility` 和 `targetCompatibility`
- 更新 JavaCompile 任务配置

### 3. UpgradeSpringBootForJava17
- 升级 Spring Boot 版本到与 Java 17 兼容的版本
- 支持从旧版本（1.x, 2.0-2.6）升级到 2.7.18 或更高版本
- 智能版本比较，避免降级

### 4. FixJava17Compatibility
- 修复 Java 17 兼容性问题
- 处理已废弃的 API（如 Thread.stop(), finalize() 等）
- 处理已移除的包（如 sun.misc.*）
- 添加迁移建议注释

### 5. FixSpringBootJava17Compatibility
- 修复 Spring Boot 特定的 Java 17 兼容性问题
- 处理废弃的注解和配置
- 更新 WebMvcConfigurerAdapter 等废弃类的使用

### 6. RemoveDeprecatedDependencies
- 移除与 Java 17 不兼容的依赖
- 移除 spring-loaded 等废弃依赖
- 清理 JAXB 相关依赖（已包含在 JDK 中）

### 7. Java8ToJava17Migration
- 组合所有子 Recipe 的主 Recipe
- 提供完整的升级流程
- 支持自定义 Spring Boot 目标版本

### 8. AI 服务模块
- 集成 ChatGLM、DeepSeek 等 AI 服务
- 支持代码分析和迁移建议生成
- 统一的 AI 服务接口，易于扩展
- 自动从 .env 文件加载 API 密钥
- 详细文档请参考 [AI 模块 README](src/main/java/com/phodal/rewrite/java8to17/ai/README.md)

## 使用方法

### 1. 安装 Recipe

```bash
cd java8-to-java17-recipes
mvn clean install
```

### 2. 在目标项目中配置

在需要升级的项目的 `pom.xml` 中添加：

```xml
<plugin>
    <groupId>org.openrewrite.maven</groupId>
    <artifactId>rewrite-maven-plugin</artifactId>
    <version>5.42.0</version>
    <configuration>
        <activeRecipes>
            <recipe>com.phodal.rewrite.java8to17.Java8ToJava17Migration</recipe>
        </activeRecipes>
    </configuration>
    <dependencies>
        <dependency>
            <groupId>com.phodal.rewrite</groupId>
            <artifactId>java8-to-java17-recipes</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
    </dependencies>
</plugin>
```

### 3. 运行升级

```bash
mvn rewrite:run
```

## 测试验证

项目包含了完整的测试用例，验证了：
- Maven Java 版本升级的正确性
- Spring Boot 版本升级逻辑
- 完整的迁移流程

我们在 `spring-boot-spring-loaded-java8-example` 项目上成功验证了升级效果：

**升级前：**
- Java 版本：1.8
- Maven 编译器插件：3.1
- Spring Boot：1.2.4.RELEASE
- 包含废弃的 spring-loaded 依赖

**升级后：**
- Java 版本：17
- Maven 编译器插件：3.6.2（支持 Java 17）
- 使用现代的 `<release>` 配置
- 移除了废弃依赖

## 技术特点

1. **模块化设计**：每个 Recipe 专注于特定的升级任务
2. **智能版本检测**：避免不必要的降级操作
3. **兼容性修复**：自动处理常见的 Java 17 兼容性问题
4. **可配置性**：支持自定义目标版本
5. **全面测试**：包含单元测试和集成测试

## 扩展性

这个工具框架可以轻松扩展以支持：
- 其他 Java 版本的升级（如 Java 17 到 Java 21）
- 更多框架的兼容性修复
- 自定义的代码转换规则
- 特定项目的迁移需求

## 参考资料

- [OpenRewrite 官方文档](https://docs.openrewrite.org/)
- [Recipe 开发指南](https://docs.openrewrite.org/authoring-recipes/recipe-development-environment)
- [Java 17 迁移指南](https://docs.oracle.com/en/java/javase/17/migrate/)
